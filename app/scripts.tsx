"use client";

import { GTM_CONFIG, OSANO_CONFIG } from "@/configs/analytics";
import Script from "next/script";

export function Scripts() {
  const oapName = `${
    process.env.NEXT_PUBLIC_OAP_NAME
  }_${process.env.NEXT_PUBLIC_NODE_ENV?.toUpperCase()}`;

  if (!oapName || !GTM_CONFIG[oapName]) {
    console.log("No oapName or GTM_CONFIG[oapName]", oapName);
  }
  if (!OSANO_CONFIG[oapName]) {
    console.log("No oapName or OSANO_CONFIG[oapName]", oapName);
  }

  const gtmConfig = GTM_CONFIG[oapName];
  const osanoId = OSANO_CONFIG[oapName];
  const isProd = process.env.NEXT_PUBLIC_NODE_ENV === "prod";

  return (
    <>
      {/* Osano Style */}
      <style jsx global>{`
        .osano-cm-button--type_denyAll {
          display: none !important;
        }
        .osano-cm-widget:focus,
        .osano-cm-widget:hover {
          outline: none !important;
        }
      `}</style>
      {/* Google Consent Script */}
      <Script id="google-consent" strategy="beforeInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('consent','default',{
            'ad_storage':'denied',
            'analytics_storage':'denied',
            'ad_user_data':'denied',
            'ad_personalization':'denied',
            'wait_for_update': 500
          });
          gtag("set", "ads_data_redaction", true);
        `}
      </Script>
      {/* Osano Script */}
      {osanoId && (
        <Script
          src={`https://cmp.osano.com/${osanoId}/osano.js`}
          strategy="beforeInteractive"
          async
        />
      )}
      {/* Google Tag Manager */}
      {gtmConfig ? (
        <Script id="google-tag-manager" strategy="beforeInteractive">
          {isProd
            ? // Production GTM script
              `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${gtmConfig.id}');
          `
            : // Development GTM script
              `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl+ '&gtm_auth=${gtmConfig.auth}&gtm_preview=${gtmConfig.preview}&gtm_cookies_win=x';f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${gtmConfig.id}');
          `}
        </Script>
      ) : null}
    </>
  );
}

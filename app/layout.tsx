import type { Metadata } from "next";
import "./globals.css";
import { injectThemeStyle } from "@/helpers/ThemeStyle";
import { getOapDetail } from "@/api/api";
import Providers from "./provider";
import { SSRInjectStyles } from "@/helpers/SSRInjectStyles";
import { Toaster } from "react-hot-toast";
import { Scripts } from "./scripts";
import { GTM_CONFIG } from "@/configs/analytics";
import { azoSans } from "./fonts";

// Utility function to validate font names and prevent empty href preload errors
function isValidFontName(fontName: string | undefined | null): boolean {
  return Boolean(
    fontName &&
      typeof fontName === "string" &&
      fontName.trim() !== "" &&
      fontName !== "undefined" &&
      fontName !== "null"
  );
}

export const metadata: Metadata = {
  title: process.env.NEXT_PUBLIC_OAP_TITLE,
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const res = await getOapDetail({
    name: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const themeStyle = {
    // Only set --body-font-family if we have a valid, non-empty font name to prevent empty href preload errors
    ...(isValidFontName(res?.theme?.["font-family"]?.name) && {
      "--body-font-family": res.theme["font-family"].name,
    }),
    ...(res?.theme?.colors || {}),
  };

  injectThemeStyle(themeStyle);

  const oapName = `${
    process.env.NEXT_PUBLIC_OAP_NAME
  }_${process.env.NEXT_PUBLIC_NODE_ENV?.toUpperCase()}`;

  // Check if oapName exists and if it has a corresponding entry in GTM_CONFIG
  if (!oapName || !GTM_CONFIG[oapName]) {
    console.log("No oapName or GTM_CONFIG[oapName]", oapName);
  }

  const gtmConfig = GTM_CONFIG[oapName];
  const isProd =
    process.env.NEXT_PUBLIC_NODE_ENV?.toLocaleLowerCase() === "prod";

  // Generate noscript HTML as string
  const gtmNoScriptHTML = gtmConfig
    ? `<iframe
         src="${
           isProd
             ? `https://www.googletagmanager.com/ns.html?id=${gtmConfig.id}`
             : `https://www.googletagmanager.com/ns.html?id=${gtmConfig.id}&gtm_auth=${gtmConfig.auth}&gtm_preview=${gtmConfig.preview}&gtm_cookies_win=x`
         }"
         height="0"
         width="0"
         style="display:none;visibility:hidden">
       </iframe>`
    : "";

  return (
    <html lang="en">
      <head>
        <SSRInjectStyles />
        <Scripts />
        {/* Only render favicon if we have a valid title */}
        {process.env.NEXT_PUBLIC_OAP_TITLE && (
          <link
            rel="icon"
            href={`/${process.env.NEXT_PUBLIC_OAP_TITLE.toLocaleLowerCase()}_logo.svg`}
            sizes="any"
          />
        )}
      </head>
      <body className={azoSans.variable}>
        {/* Google Tag Manager NoScript (Body) */}
        {gtmConfig && gtmNoScriptHTML && (
          <noscript dangerouslySetInnerHTML={{ __html: gtmNoScriptHTML }} />
        )}
        <Providers>{children}</Providers>
        <Toaster
          position="top-center"
          toastOptions={{
            custom: {
              duration: 2000,
            },
          }}
        />
      </body>
    </html>
  );
}

import {
  deleteDocument,
  getAccessToken,
  getPreSignedURL,
  getUpdatedDoc,
} from "@/api/api";
import { consumerAPIKey } from "@/lib/atom";
import { useAtom } from "jotai";

export const useFile = () => {
  const [apiKey] = useAtom(consumerAPIKey);
  const uploadFile = async ({
    payload,
    email,
    watch = () => {},
    applicationId,
    oapName,
    businessUnitFilter,
  }: {
    payload: Object;
    email: any;
    studentDetail?: any;
    watch?: Function;
    studentDocuments?: any;
    applicationId: string;
    oapName: string;
    businessUnitFilter?: string;
  }) => {
    try {
      const newPayload: any = {
        ...payload,
        email,
        applicationId,
        businessUnitFilter,
        oapName,
        //@ts-ignore
        documentName: payload?.customDocumentName || payload?.path,
      };

      const { path, file, pathName, ...rest } = newPayload;

      const result = await getPreSignedURL(
        {
          contentType: newPayload.type,
          // path: `${newPayload.email}/${newPayload.documentType}/${newPayload.path}`,
          ...rest,
        },
        apiKey,
        await getAccessToken()
      );

      let presignedUrlResponse: any;
      if (result?.signedUrl) {
        presignedUrlResponse = await fetch(result?.signedUrl, {
          method: "PUT",
          headers: {
            "Content-Type": newPayload.contentType,
          },
          body: newPayload?.file,
        });
      }
      const uploadDocResponse = await getUpdatedDoc(
        {
          contentType: newPayload.type,
          ...rest,
        },
        apiKey,
        await getAccessToken()
      );

      return {
        ...newPayload,
        ocrFields: newPayload.processOcr ? uploadDocResponse : {},
        successOcrFields: newPayload.processOcr
          ? !uploadDocResponse.message
          : null,
        path,
        file,
        pathName,
        status: true,
      };
    } catch (error: any) {
      console.log({ error });
      return { message: error?.message, status: false };
    }
  };

  const deleteFile = async ({
    payload,
    studentDetail,
    watch = () => {},
  }: {
    payload: any;
    studentDetail?: any;
    watch?: any;
  }) => {
    try {
      const response = await deleteDocument(
        studentDetail,
        apiKey,
        await getAccessToken()
      );

      //  deleteUploadFile(payload);
      // if (response?.message) {
      //   return toast({
      //     description: response?.message,
      //   });
      // }
      // const newMetaData = handleMetaData({ metaData, studentDetail });
      // const percentage = handleProgressCalculation({
      //   studentDetail,
      //   metaData: newMetaData,
      //   watch,
      // });
      // const updatePayload = {
      //   progressPercentage: percentage,
      //   email: studentDetail.email,
      // };
      // await updateStudentDetail(updatePayload);
      // return { progressPercentage: percentage };
    } catch (error) {
      console.log({ error });
    }
  };
  return { uploadFile, deleteFile };
};

{"name": "oap-frontend", "version": "1.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/devtools": "^4.3.1", "@next/font": "^14.0.4", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-icons": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-tooltip": "latest", "@tanstack/query-sync-storage-persister": "^5.17.0", "@tanstack/react-query": "^5.14.6", "@tanstack/react-query-devtools": "^5.15.0", "@tanstack/react-query-persist-client": "^5.17.0", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.6", "amazon-cognito-identity-js": "^6.3.15", "aws-amplify": "^6.5.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.6.0", "dompurify": "^3.0.6", "embla-carousel-react": "^8.6.0", "jotai": "^2.6.1", "jsdom": "^23.0.1", "libphonenumber-js": "^1.10.56", "lucide-react": "0.518.0", "next": "15.3.4", "react": "19.1.0", "react-datepicker": "^8.4.0", "react-day-picker": "8.10.1", "react-dom": "19.1.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.4.1", "react-intl-tel-input": "^8.2.0", "react-markdown": "^9.0.1", "react-modal": "^3.16.1", "react-phone-input-2": "^2.15.1", "react-select": "^5.8.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1"}, "overrides": {"react": "$react", "react-dom": "$react-dom"}, "devDependencies": {"@tailwindcss/typography": "^0.5.9", "@types/node": "^20.8.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-modal": "^3.16.3", "@types/react-signature-canvas": "^1.0.7", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.16", "eslint": "^8.50.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "react-signature-canvas": "1.0.7", "tailwindcss": "^3.3.0", "typescript": "^5.2.2"}}
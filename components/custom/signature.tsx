import React, { useRef, useState, useEffect } from "react";
import SignatureCanvas from "react-signature-canvas";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { applicationId, consumerAPIKey, email } from "@/lib/atom";
import { useAtom } from "jotai";
import { useFile } from "@/hooks/useFile";
import { v4 as uuidv4 } from "uuid";
import { Label } from "../ui/label";
import { Check, ImageIcon, Trash2 } from "lucide-react";

interface SignatureProps {
    displayName: string;
    handleFileChange: (file: any) => void;
    uploadedFiles?: any[];
    handleDeleteFile?: Function;
    isMandatory?: boolean;
    errorMessage?: string;
    register?:any;
}

const Signature: React.FC<SignatureProps> = ({
    displayName,
    handleFileChange,
    uploadedFiles = [],
    handleDeleteFile,
    isMandatory = false,
    errorMessage,
    register
}) => {
    const sigCanvas = useRef<SignatureCanvas>(null);
    const [signatureData, setSignatureData] = useState<string | null>(null);
    const fileInputRef = useRef<any>(null);
    const [application] = useAtom(applicationId);
    const [userEmail] = useAtom(email);
    const [progressingFileDetail, setProgressingFileDetail] = useState<any>(false);
    const [showSaveButton, setShowSaveButton] = useState<any>(true);
    const [signature, setSignature] = useState<File | null>(null);
    const [showSignaturePad, setShowSignaturePad] = useState<Boolean>(true);
    const [showLoader, setShowLoader] = useState<Boolean>(false);
    const [documentId, setDocumentId] = useState<string | null>(null);

    const { uploadFile, deleteFile } = useFile();

    // Load existing signature if available
    useEffect(() => {
        if (uploadedFiles && uploadedFiles.length > 0) {
            const existingSignature = uploadedFiles.find(file => file.documentType === "Signature");
            if (existingSignature) {
                setSignatureData(existingSignature.dataUrl);
                setDocumentId(existingSignature.documentId);
                setShowSaveButton(false);
                setShowSignaturePad(false);
                setSignature(existingSignature);
            }
        }
    }, [uploadedFiles]);



    const clearSignature = async (event: React.MouseEvent<HTMLButtonElement>) => {
        event.preventDefault();
        sigCanvas.current?.clear();
        setSignatureData(null);
        const payload = {
            fileData: {
                documentId,
                name: signature?.name,
                size: signature?.size,
                type: signature?.type,
                lastModified: signature?.lastModified,
            },
            documentType: "Signature",
        };
        setShowLoader(true);
        try {
            await deleteFile({
                payload: payload.fileData,
                studentDetail: {
                    email: userEmail,
                    oapName: process.env.NEXT_PUBLIC_OAP_NAME,
                    applicationId: application,
                    name: payload.fileData.name,
                    type: payload.documentType,
                    documentId: payload.fileData.documentId,
                },
            });
            setDocumentId(null);
            setShowLoader(false);
            setShowSignaturePad(true);
            setShowSaveButton(true);
            handleFileChange([]);
        } catch (error) {
            setShowLoader(false);
        }
    };

    const saveSignature = async (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        if (!sigCanvas.current) return;

        if (sigCanvas.current.isEmpty()) {
            console.log("Please sign");
            return;
        }
        setShowSaveButton(false);
        setProgressingFileDetail(true);

        const dataUrl = sigCanvas.current.getCanvas().toDataURL("image/png");
        const blob = await fetch(dataUrl).then((res) => res.blob());
        const file = new File([blob], "signature.png", { type: "image/png" });
        const generatedDocumentId = uuidv4();

        setSignatureData(dataUrl);

        if (fileInputRef.current) {
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInputRef.current.files = dataTransfer.files;
            setSignature(fileInputRef.current.files[0]);
        }


        try {
            const payload = {
                file: fileInputRef.current.files[0],
                path: `${application}/Signature/${generatedDocumentId}.png`,
                pathName: "signature",
                documentFormat: "png",
                documentType: "Signature",
                contentType: "image/png",
                customDocumentName: "signature.png",
                documentId: generatedDocumentId,
            }
            setProgressingFileDetail(true);
            const response = await uploadFile({
                email: userEmail,
                applicationId: application,
                oapName: process.env.NEXT_PUBLIC_OAP_NAME as string,
                payload,
            });

            if (response.status) {
                const signatureFile = {
                    oapName: process.env.NEXT_PUBLIC_OAP_NAME,
                    path: `${application}/Signature/${generatedDocumentId}.png`,
                    documentType: "Signature",
                    documentId: generatedDocumentId,
                    createdAt: new Date().toISOString(),
                    email: userEmail,
                    contentType: "image/png",
                    bucketName: "reviewcenter-stage",
                    documentName: "signature.png",
                    documentFormat: "png",
                    SK: `${process.env.NEXT_PUBLIC_OAP_NAME}_${application}_Signature_${generatedDocumentId}`,
                    PK: userEmail,
                    applicationId: application,
                };

                handleFileChange([signatureFile]);
                setShowSaveButton(false);
                setDocumentId(generatedDocumentId);
                setShowSignaturePad(false);
            } else {
                console.error("Upload failed:", response.message);
                setShowSaveButton(true);
            }
        } catch (error) {
            console.error("Error uploading signature:", error);
        } finally {
            setProgressingFileDetail(false);
        }
    };

    return (
        <>
            <div className="mb-3">
                <Label className="mb-10">
                    {displayName}{" "}
                    {isMandatory ? (
                        <Label className="text-sm ml-1 text-red-500">*</Label>
                    ) : null}
                </Label>
            </div>
            <div className={`${showSignaturePad ? ' mb-3 border-2 border-solid' : 'border-2 border-dashed'} rounded-md mt-2 mb-3`}>
                {showLoader ? (
                    <div className="flex justify-center p-4">
                        <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full"></div>
                    </div>
                ) : (
                    <div className={showSignaturePad ? "p-4" : "p-0"}>
                        {showSignaturePad ? (
                            <>
                                <div className="border rounded-lg overflow-hidden">
                                    <SignatureCanvas
                                        ref={sigCanvas}
                                        penColor="black"
                                        minWidth={1}
                                        maxWidth={1}
                                        dotSize={2}
                                        canvasProps={{
                                            className: `w-full h-40 sm:h-52 bg-white font-light sigCanvas ${progressingFileDetail && 'cursor-not-allowed'}`,
                                        }}
                                    />
                                    {progressingFileDetail && (
                                        <div className="absolute inset-0 bg-white bg-opacity-25 cursor-not-allowed"></div>
                                    )}
                                </div>

                                <div className="flex justify-between mt-4">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={clearSignature}
                                        disabled={progressingFileDetail}
                                    >
                                        Clear
                                    </Button>

                                    {progressingFileDetail ? (
                                        <Button disabled className="text-white bg-gray-400">
                                            Uploading...
                                        </Button>
                                    ) : (
                                        showSaveButton && (
                                            <Button
                                                className="text-white hover:opacity-80"
                                                type="button"
                                                onClick={saveSignature}
                                            >
                                                Save
                                            </Button>
                                        )
                                    )}
                                </div>

                            </>
                        ) : Array.isArray(uploadedFiles) && (
                            <div className="p-5">
                                <div className="flex flex-row justify-between border-b-0 flex-wrap">
                                    <div className="flex flex-row flex-wrap flex-1">
                                        <ImageIcon className="h-[18px] w-[18px]" />
                                        <Label className="text-body2 mx-10 truncate max-w-[160px] sm:max-w-[220px]">
                                            {signature?.name || "Signature"}
                                        </Label>
                                        <div className="h-[10px] w-[10px] rounded-full bg-green-500 flex items-center justify-center self-center">
                                            <Check className="h-[8px] w-[8px]" color="#ffffff" />
                                        </div>
                                    </div>
                                    <button
                                        type="button"
                                        onClick={clearSignature}
                                        className="border-none bg-transparent p-0"
                                    >
                                        <Trash2
                                            className="h-[18px] w-[18px] cursor-pointer"
                                            color="#000000"
                                        />
                                    </button>
                                </div>
                            </div>
                        )}

                        <input type="file" ref={fileInputRef} className="hidden" />
                    </div>
                )}

            </div>
            {errorMessage && (
                <Label className="text-red-500 text-sm">
                    {errorMessage}
                </Label>
            )}
        </>
    );
};

export default Signature;

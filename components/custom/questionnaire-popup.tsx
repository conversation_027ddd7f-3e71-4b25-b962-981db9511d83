import React, { useState } from "react";
import Modal from "react-modal";
import { <PERSON><PERSON> } from "@/components/ui/button";
import DynamicFields from "./DynamicFields";
import { Label } from "../ui/label";
import { useForm, FormProvider } from "react-hook-form";
import Image from "next/image";
import { Separator } from "../ui/separator";
import { cn } from "@/lib/utils";
import errorX from "../../public/error-x.svg";
import { useQuery } from "@tanstack/react-query";
import { getOapDetail } from "@/api/api";
import { useAtom } from "jotai";
import { consumerAPIKey } from "@/lib/atom";
import { useRouter } from "next/navigation";

export const QuestionnairePopup = ({
  isOpen,
  onClose,
  fieldData,
  basicDetails,
  onSubmit,
  sectionQuery,
  onCancelationApplication,
  questionariesSectionQuery,
}: {
  isOpen: boolean;
  onClose: Function;
  onCancelationApplication: Function;
  fieldData: any;
  basicDetails: any;
  onSubmit: Function;
  sectionQuery: any;
  questionariesSectionQuery: any;
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [applicationFields, setApplicationFields] = useState<any>({});
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [apiKey] = useAtom(consumerAPIKey);
  const [pageDetails, setPageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });
  const router = useRouter();
  const { data: pageQuery } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
        },
        apiKey
      );
      return res;
    },
    enabled: true,
  });

  const methods = useForm();
  const {
    handleSubmit,
    formState: { errors },
    register,
    watch,
    trigger,
    setError,
    clearErrors,
  } = methods;

  const filteredQuestions = fieldData?.filter((item: any) => {
    return (
      basicDetails[item?.visibleWhen?.fieldName] === item?.visibleWhen?.value
    );
  });

  const currentQuestion = filteredQuestions[currentQuestionIndex];

  const handleValueChanged = async (selectedValue: any, item: any) => {
    clearErrors(item.fieldName);
    setApplicationFields((prev: any) => {
      if (item.type === "pickList" || item.type === "dropDown") {
        return {
          ...prev,
          [item.fieldDisplayName]: selectedValue.value,
          [`${item.fieldDisplayName}_isQualified`]:
            item.endUpWhen === selectedValue ? "Unqualified" : "Qualified",
        };
      } else {
        return {
          ...prev,
          [item.fieldName]: selectedValue,
          [`${item.fieldName}_isQualified`]:
            item.endUpWhen === selectedValue ? "Unqualified" : "Qualified",
        };
      }
    });
  };

  const handleNext = async () => {
    const selectedValue =
      currentQuestion?.type === "pickList" ||
      currentQuestion?.type === "dropDown"
        ? applicationFields[currentQuestion.fieldDisplayName]
        : applicationFields[currentQuestion.fieldName];

    if (currentQuestion.isMandatory && !selectedValue) {
      setError(currentQuestion.fieldName, {
        type: "manual",
        message: currentQuestion.rules.message,
      });
      return;
    }

    if (
      currentQuestion.endUpWhen &&
      currentQuestion.endUpWhen === selectedValue
    ) {
      setShowErrorModal(true);
      handleFormCancelation();
    } else if (currentQuestionIndex < filteredQuestions.length - 1) {
      setCurrentQuestionIndex((next) => next + 1);
    } else {
      handleFormSubmit();
    }
  };

  const handleFormCancelation = handleSubmit(() => {
    onCancelationApplication({
      ...applicationFields,
      notMetEligibilityCreteriaWhen: currentQuestion.displayName,
      notMetEligibilityCreteria: true,
    });
  });

  const handleFormSubmit = handleSubmit(() => {
    onSubmit({ ...applicationFields, notMetEligibilityCreteria: false });
    onClose();
  });

  if (showErrorModal) {
    return (
      <Modal
        className="fixed inset-0 flex-1  items-center flex flex-col p-6 outline-none justify-between"
        overlayClassName="fixed inset-0 bg-on-background "
        isOpen
      >
        <div className="items-center flex flex-col mt-8 space-y-8 p-6">
          <Image
            className="header -brand-image"
            src={sectionQuery?.logoInfo?.signedUrl}
            alt="ucw_logo_int"
            width={100}
            height={200}
          />
          <div className="rounded-lg bg-white w-[480px] p-10 shadow-lg space-y-8 items-center">
            <div className="flex justify-center mb-8">
              <Image src={errorX} width={60} height={10} alt="error" />
            </div>

            <p className="text-lg tracking-wide font-bold text-center leading-6">
              {questionariesSectionQuery.invalidMessage
                ? questionariesSectionQuery.invalidMessage.replace(
                    "<institution>",
                    process.env.NEXT_PUBLIC_OAP_NAME
                  )
                : `Unfortunately, you do not meet the eligibility criteria to study
              at ${process.env.NEXT_PUBLIC_OAP_NAME}.`}
            </p>
            <Button
              onClick={() => {
                window.location.reload();
              }}
              className="button bg-secondary w-full hover:bg-primary text-white font-bold px-6 py-2 rounded cursor-pointer"
            >
              Create New Application
            </Button>
          </div>
        </div>
        <div className="mb-4 mt-4">
          <p className="text-text-secondary text-xs">
            {pageQuery?.layout?.collegeInfo?.text}{" "}
            <a
              href={pageQuery?.layout?.collegeInfo?.linkUrl}
              target="_blank"
              className="underline cursor-pointer"
            >
              {pageQuery?.layout?.collegeInfo?.linkText}
            </a>
          </p>
          <span className=" w-full text-text-secondary text-xs flex justify-center my-[2px] pb-[2px]">
            v{process.env.VERSION}
          </span>
        </div>
      </Modal>
    );
  }

  return (
    <div className="min-h-screen">
      <Modal
        isOpen={isOpen}
        className="fixed  inset-0 flex flex-col justify-between items-center  outline-none pt-16  overflow-y-scroll"
        overlayClassName="fixed inset-0 bg-on-background"
      >
        <div className="flex flex-col items-center outline-none">
          <FormProvider {...methods}>
            <div>
              <Image
                className="header -brand-image"
                src={sectionQuery?.logoInfo?.signedUrl}
                alt="ucw_logo_int"
                width={100}
                height={40}
                priority={true}
              />
            </div>
            <form onSubmit={handleFormSubmit}>
              <div className="bg-background w-[480px] p-10  mt-12 rounded-lg  shadow-xl flex-col space-y-10">
                <div className="space-y-3">
                  <p className="text-3xl font-semibold flex justify-center">
                    Qualifying Questions
                  </p>
                  <p className="text-md text-center">
                    Please, answer some questions before registration
                  </p>
                </div>

                <div className="gap-x-3 flex">
                  {[...Array(filteredQuestions.length)].map((_, i) => (
                    <Separator
                      key={i}
                      className={cn(
                        `w-14 h-1 rounded-md ${
                          currentQuestionIndex >= i
                            ? "bg-secondary"
                            : "bg-slate-300"
                        }`
                      )}
                    />
                  ))}
                </div>

                <div className="bg-gray-100 p-3 rounded-lg">
                  <div className="mb-3">
                    <Label className="text-md font-semibold">
                      {currentQuestion?.displayName}
                      {currentQuestion?.isMandatory && (
                        <Label className="text-sm ml-1 text-red-500">*</Label>
                      )}
                    </Label>{" "}
                  </div>

                  <DynamicFields
                    arrayIndex={currentQuestionIndex}
                    watch={watch}
                    getLookupData={currentQuestion?.pickListValues || []}
                    key={currentQuestion?.fieldName}
                    register={register}
                    fieldItem={currentQuestion}
                    fieldType={currentQuestion?.type}
                    fieldName={currentQuestion?.fieldName}
                    isVisibleWhen
                    trigger={trigger}
                    fromApplicationFilter={true}
                    selectedValue={
                      currentQuestion?.type === "pickList" ||
                      currentQuestion?.type === "dropDown"
                        ? applicationFields[currentQuestion.fieldDisplayName]
                        : applicationFields[currentQuestion?.fieldName]
                    }
                    handleValueChanged={(value: any) =>
                      handleValueChanged(value, currentQuestion)
                    }
                    errorMessage={errors[currentQuestion?.fieldName]?.message}
                  />
                </div>

                <div className="mt-6 flex justify-end gap-x-3">
                  <Button
                    type="button"
                    onClick={handleNext}
                    className="button button_primary bg-secondary w-full hover:bg-primary text-white font-bold px-6 py-2 rounded cursor-pointer"
                  >
                    {currentQuestionIndex === filteredQuestions.length - 1
                      ? "Submit"
                      : "Next"}
                  </Button>
                </div>
              </div>
            </form>
          </FormProvider>
        </div>

        <div className="mb-4 mt-4 self-center ">
          <p className="text-text-secondary text-xs">
            {pageQuery?.layout?.collegeInfo?.text}{" "}
            <a
              href={pageQuery?.layout?.collegeInfo?.linkUrl}
              target="_blank"
              className="underline cursor-pointer"
            >
              {pageQuery?.layout?.collegeInfo?.linkText}
            </a>
          </p>
          <span className=" w-full text-text-secondary text-xs flex justify-center my-[2px] pb-[2px]">
            v{process.env.VERSION}
          </span>
        </div>
      </Modal>
    </div>
  );
};

import React from "react";
import { useAtom } from "jotai";
import { staticContentsAtom } from "@/lib/atom";

interface SavingModalProps {
  isOpen: boolean;
}

const SavingModal: React.FC<SavingModalProps> = ({ isOpen }) => {
  
  const [staticContents] = useAtom<any>(staticContentsAtom);
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-background rounded-lg shadow-xl p-8 flex flex-col items-center justify-center max-w-md w-full mx-4">
        <div className="mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 100 100"
            width="80"
            height="80"
            className="text-primary"
          >
            <defs>
              <linearGradient
                id="savingGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop
                  offset="0%"
                  stopColor="currentColor"
                  stopOpacity="0.8"
                />
                <stop
                  offset="100%"
                  stopColor="currentColor"
                  stopOpacity="0.2"
                />
              </linearGradient>
            </defs>

            {/* Rotating outer ring */}
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="url(#savingGradient)"
              strokeWidth="4"
              strokeDasharray="8,4"
              opacity="0.8"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                from="0 50 50"
                to="360 50 50"
                dur="2s"
                repeatCount="indefinite"
              />
            </circle>

            {/* Inner rotating ring (opposite direction) */}
            <circle
              cx="50"
              cy="50"
              r="32"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeDasharray="4,8"
              opacity="0.4"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                from="360 50 50"
                to="0 50 50"
                dur="1.5s"
                repeatCount="indefinite"
              />
            </circle>

            {/* Center pulsing dot */}
            <circle
              cx="50"
              cy="50"
              r="8"
              fill="currentColor"
              opacity="0.6"
            >
              <animate
                attributeName="r"
                values="6;10;6"
                dur="1s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="opacity"
                values="0.4;0.8;0.4"
                dur="1s"
                repeatCount="indefinite"
              />
            </circle>
          </svg>
        </div>

        <h3 className="text-xl font-semibold text-foreground mb-2">
          {staticContents?.application?.save || "Saving Application"}
        </h3>
        <p className="text-muted-foreground text-center mb-4">
          {staticContents?.application?.wait || "Please wait while we save your application. Do not close this window or navigate away."}
        </p>

        <div className="w-full bg-muted h-2 rounded-full overflow-hidden">
          <div
            className="bg-primary h-full animate-pulse"
            style={{ width: "100%" }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default SavingModal;

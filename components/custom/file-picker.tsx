import React, { useRef, useState } from "react";
import { Label } from "../ui/label";
import {
  Check,
  ImagePlusIcon,
  Trash2,
  Image as ImageIcon,
  AlertCircle,
} from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import Loader from "./loader";
import toast from "react-hot-toast";
import { useAtom } from "jotai";
import { staticContentsAtom } from "@/lib/atom";
import SuccessNotificationModal from "./success-notification-modal";
import OcrLoaderModal from "./ocr-loader-modal";
import ErrorNotificationModal from "./error-notification-modal";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

interface FilePickerProps {
  documentType?: string;
  handleDelete?: Function;
  handleFileChange: Function;
  heading: string;
  isMandatory?: boolean;
  isMultiUpload?: boolean;
  keyValue: string;
  uploadedFiles?: any[];
  uploadFile?: Function;
  register: any;
  name?: string;
  warning?: string;
  maxFiles: number;
  displaySubInformation: string;
  pdfLabel?: any;
  allowedFileTypes?: any;
  setValue?: any;
  fieldItem?: any;
  watch?: any;
  isOcrProcessDocument?: boolean;
  maxOcrReprocessCount: number;
}

const FilePicker: React.FC<FilePickerProps> = ({
  register,
  documentType,
  handleDelete = () => {},
  handleFileChange,
  heading = "",
  isMandatory = false,
  isMultiUpload = false,
  keyValue,
  uploadedFiles = [],
  uploadFile = () => {},
  name,
  maxFiles,
  warning,
  displaySubInformation,
  pdfLabel,
  allowedFileTypes,
  setValue,
  fieldItem,
  watch,
  isOcrProcessDocument,
  maxOcrReprocessCount,
}) => {
  const [error, setError] = useState<string>();
  const [progressingFileDetail, setProgressingFileDetail] = useState<any>();
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [errorFetchingMessage, setErrorFetchingMessage] = useState("");
  const [showOcrModal, setShowOcrModal] = useState(false);
  const [isSuccessOcrFields, setIsSuccessOcrFields] = useState(false);
  const documentRef = useRef<any>(null);
  const fileTypes = allowedFileTypes || [
    "application/pdf",
    "image/jpg",
    "image/jpeg",
    "image/png",
  ];
  const [staticContent] = useAtom<any>(staticContentsAtom);
  const [ocrResponse, setOcrResponse] = useState<any>(null);
  const [fontSize] = useAtom(fontSizeAtom);

  const handleDragOver = (event: any) => {
    event.preventDefault();
  };

  const handleFilePicker = async (event: any) => {
    const uuid = uuidv4();
    setError("");
    const uploadedFile = event.target.files[0];
    const isValidFile = uploadedFile ? isFileValid(uploadedFile) : false;
    if (isValidFile) {
      uploadedFile.documentId = uuid;

      const isFileNameAvailable = uploadedFiles.some(
        (file) => file.name === uploadedFile?.name
      );
      if (isFileNameAvailable) {
        setError(
          staticContent?.errors?.fileUpload?.duplicateFile ||
            "Same file is already uploaded"
        );
      } else if (maxFiles && uploadedFiles.length >= maxFiles) {
        toast.custom(
          <div className="bg-error flex items-center gap-x-1 p-2 rounded">
            <div className="w-6">
              <AlertCircle
                name="shield-alert"
                height={20}
                width={20}
                color={"var(--color-background)"}
              />
            </div>
            <p className="text-background text-sm">
              {staticContent?.errors?.fileUpload?.maxFilesExceeded?.replace(
                "{maxFiles}",
                maxFiles.toString()
              ) || `You can upload a maximum of ${maxFiles} files.`}
            </p>
          </div>
        );
      } else {
        setProgressingFileDetail(true);

        // Show OCR modal if OCR processing is enabled
        if (
          isOcrProcessDocument &&
          watch("ocrReprocessCount") < maxOcrReprocessCount
        ) {
          setShowOcrModal(true);
        }

        const response = await uploadFile({
          file: uploadedFile,
          path: uploadedFile.name,
          pathName: uploadedFile.name.split(".").slice(0, -1).join("."),
          documentFormat: uploadedFile.name.split(".").pop(),
          documentType,
          contentType: uploadedFile.type,
          documentId: uuid,
          ...(isOcrProcessDocument &&
            watch("ocrReprocessCount") < maxOcrReprocessCount && {
              customDocumentName: uploadedFile.name,
              processOcr: isOcrProcessDocument,
            }),
        });

        handleFileChange([...uploadedFiles, uploadedFile]);
        if (response?.success) {
          handleFileChange([...uploadedFiles, uploadedFile]);

          if (isOcrProcessDocument) {
            if (response?.successOcrFields) {
              const firstNameLimit = fieldItem?.fieldData?.find(
                (field: any) => field.fieldName === "passportName"
              )?.limit;
              if (response.ocrFields.firstName?.length > firstNameLimit) {
                setOcrResponse(response);
                setSuccessMessage(fieldItem?.successMessage);
                setShowSuccessModal(true);
              } else {
                setIsSuccessOcrFields(true);
                setSuccessMessage(fieldItem?.successMessage);
                setShowSuccessModal(true);
                setErrorFetchingMessage("");
              }
            }
            if (!response?.successOcrFields) {
              setIsSuccessOcrFields(false);
              if (watch("ocrReprocessCount") === 1) {
                setErrorFetchingMessage(fieldItem?.errorMessage1);
                setErrorMessage(fieldItem?.retryMessage1);
                setShowErrorModal(true);
              } else if (watch("ocrReprocessCount") === 2) {
                setErrorFetchingMessage(fieldItem?.errorMessage2);
                setErrorMessage(fieldItem?.retryMessage2);
                setShowErrorModal(true);
              }
            }
          }
        }

        if (!response?.success) {
          handleFileChange([...uploadedFiles]);
        }

        // Hide OCR modal when processing is complete
        setShowOcrModal(false);
        setProgressingFileDetail(false);
      }
    }
  };

  const handleDrop = async (event: any) => {
    const uuid = uuidv4();
    setError("");
    event.preventDefault();
    const uploadedFile = event.dataTransfer.files[0];
    const isValidFile = uploadedFile ? isFileValid(uploadedFile) : false;
    if (isValidFile) {
      uploadedFile.documentId = uuid;
      const isFileNameAvailable = uploadedFiles.some(
        (file) => file.name === uploadedFile?.name
      );

      if (isFileNameAvailable) {
        setError(
          staticContent?.errors?.fileUpload?.duplicateFile ||
            "Same file is already uploaded"
        );
      } else if (maxFiles && uploadedFiles.length >= maxFiles) {
        toast.custom(
          <div className="bg-error flex items-center gap-x-1 p-2 rounded">
            <div className="w-6">
              <AlertCircle
                name="shield-alert"
                height={20}
                width={20}
                color={"var(--color-background)"}
              />
            </div>
            <p className="text-background text-sm">
              {staticContent?.errors?.fileUpload?.maxFilesExceeded?.replace(
                "{maxFiles}",
                maxFiles.toString()
              ) || `You can upload a maximum of ${maxFiles} files.`}
            </p>
          </div>
        );
      } else {
        setProgressingFileDetail(true);

        // Show OCR modal if OCR processing is enabled
        if (
          isOcrProcessDocument &&
          watch("ocrReprocessCount") < maxOcrReprocessCount
        ) {
          setShowOcrModal(true);
        }

        const response = await uploadFile({
          file: uploadedFile,
          path: uploadedFile.name,
          pathName: uploadedFile.name.split(".").slice(0, -1).join("."),
          documentFormat: uploadedFile.name.split(".").pop(),
          documentType,
          contentType: uploadedFile.type,
          documentId: uuid,
          ...(isOcrProcessDocument &&
            watch("ocrReprocessCount") < maxOcrReprocessCount && {
              customDocumentName: uploadedFile.name,
              processOcr: isOcrProcessDocument,
            }),
        });

        if (response?.success === true) {
          handleFileChange([...uploadedFiles, uploadedFile]);

          if (isOcrProcessDocument) {
            if (response?.successOcrFields) {
              const firstNameLimit = fieldItem?.fieldData?.find(
                (field: any) => field.fieldName === "passportName"
              )?.limit;

              if (response.ocrFields.firstName?.length > firstNameLimit) {
                setOcrResponse(response);
                setSuccessMessage(fieldItem?.successMessage);
                setShowSuccessModal(true);
              } else {
                setIsSuccessOcrFields(true);
                setSuccessMessage(fieldItem?.successMessage);
                setShowSuccessModal(true);
                setErrorFetchingMessage("");
              }
            }
            if (!response?.successOcrFields) {
              setIsSuccessOcrFields(false);
              if (watch("ocrReprocessCount") === 1) {
                setErrorFetchingMessage(fieldItem?.errorMessage1);
                setErrorMessage(fieldItem?.retryMessage1);
                setShowErrorModal(true);
              } else if (watch("ocrReprocessCount") === 2) {
                setErrorFetchingMessage(fieldItem?.errorMessage2);
                setErrorMessage(fieldItem?.retryMessage2);
                setShowErrorModal(true);
              }
            }
          }
        }

        if (!response?.success) {
          handleFileChange([...uploadedFiles]);
        }

        // Hide OCR modal when processing is complete
        setShowOcrModal(false);
        setProgressingFileDetail(false);
      }
    }
  };

  const isFileValid = (uploadedFile: File) => {
    if (uploadedFile?.size > 5e6) {
      toast.custom(
        <div className="bg-error flex items-center gap-x-1 p-2 rounded">
          <div className="w-6">
            <AlertCircle
              name="shield-alert"
              height={20}
              width={20}
              color={"var(--color-background)"}
            />
          </div>
          <p className="text-background text-sm">
            {uploadedFile?.size === 0
              ? staticContent?.errors?.fileUpload?.fileSizeNotMet ||
                "Invalid file size! Please upload a file that is greater than 0 KB."
              : staticContent?.errors?.fileUpload?.fileSizeExceeded ||
                "Invalid file size! Please upload a file that is less than 5 MB."}
          </p>
        </div>
      );
      return false;
    }
    if (uploadedFile?.name?.split(".")?.slice(0, -1)?.join(".")?.length > 70) {
      toast.custom(
        <div className="bg-error flex items-center gap-x-1 p-2 rounded">
          <div className="w-6">
            <AlertCircle
              name="shield-alert"
              height={20}
              width={20}
              color={"var(--color-background)"}
            />
          </div>
          <p className="text-background text-sm">
            {staticContent?.errors?.fileUpload?.fileNameTooLong ||
              "Please shorten the file name. It must be 70 characters or less."}
          </p>
        </div>
      );
      return false;
    }
    if (!fileTypes.includes(uploadedFile.type)) {
      toast.custom(
        <div className="bg-error flex items-center gap-x-1 p-2 rounded">
          <div className="w-6">
            <AlertCircle
              name="shield-alert"
              height={20}
              width={20}
              color={"var(--color-background)"}
            />
          </div>
          <p className="text-background text-sm">
            {staticContent?.errors?.fileUpload?.invalidFormat ||
              "Invalid file format! Please upload an allowed file type."}
          </p>
        </div>
      );
      return false;
    }
    return true;
  };

  const handleFileDelete = async (fileData: any, fileIndex: number) => {
    setProgressingFileDetail(true);
    setErrorFetchingMessage("");

    const filesCopy = [...uploadedFiles];

    if (filesCopy?.length === 1) {
      filesCopy.pop();
    } else {
      filesCopy.splice(fileIndex, 1);
    }
    await handleFileChange(filesCopy);

    if (fileData.path || fileData?.name) {
      if (Array.isArray(fieldItem?.resetChild)) {
        fieldItem?.resetChild.forEach((child: any) => {
          setValue(child, "");
        });
      } else {
        setValue(fieldItem?.resetChild, "");
      }

      if (fieldItem?.resetWhenDelete) {
        const { condition, fieldNames } = fieldItem.resetWhenDelete;
        if (
          condition.type === "lessthan" &&
          watch(condition.fieldName) < condition.value
        ) {
          fieldNames.forEach((fieldName: string) => {
            setValue(fieldName, "");
          });
        }
      }

      await handleDelete({ fileData: fileData, documentType });
    }
    setProgressingFileDetail(false);
  };
  const { onBlur, onChange, ref, ...rest } = register;

  const firstNameLimit = fieldItem?.fieldData?.find(
    (field: any) => field.fieldName === "passportName"
  )?.limit;
  const isNameSplit =
    ocrResponse?.ocrFields?.firstName?.length > firstNameLimit;

  return (
    <div
      className="mb-[20px]"
      key={keyValue || fieldItem?.fieldName}
      onBlur={onBlur}
      onChange={onChange}
      {...rest}
    >
      <Label
        className="mb-10"
        style={getBrandSpecificFontStyle(fontSize, "label")}
      >
        {heading}{" "}
        {isMandatory ? (
          <Label className="text-sm ml-1 text-red-500">*</Label>
        ) : null}
      </Label>
      {warning && (
        <div className="mt-3 mb-6 flex flex-row">
          <div className=" w-6 ">
            <AlertCircle
              name="shield-alert"
              height={16}
              width={16}
              color="#CC9800"
            />
          </div>
          <Label className=" mx-2 text-sm leading-5">{warning}</Label>
        </div>
      )}
      <div className="border-2 border-dashed rounded-md mt-2 max-w-2/4">
        {(uploadedFiles?.length === 0 && !isMultiUpload) ||
        (isMultiUpload &&
          (maxFiles ? uploadedFiles.length < maxFiles : true)) ? (
          <form onDragOver={handleDragOver} onDrop={handleDrop}>
            <div>
              {!progressingFileDetail && (
                <div className="rounded-4 p-5 flex-row justify-center items-center flex flex-1">
                  <ImagePlusIcon />
                  <div className="ml-[10px] flex-wrap flex-1 flex flex-col">
                    <div className="flex-row flex mb-1 passport-name-file ">
                      <input
                        id={name}
                        type="file"
                        ref={documentRef}
                        className="hidden"
                        onChangeCapture={(e) => {
                          handleFilePicker(e);
                        }}
                        {...rest}
                        onBlur={onBlur}
                      />
                      <Label>
                        {(isMultiUpload
                          ? staticContent?.fileUpload?.dropFilesText
                          : staticContent?.fileUpload?.dropFileText) ||
                          `Drop ${
                            isMultiUpload ? "files" : "file"
                          } to attach, or `}
                      </Label>
                      <Label
                        htmlFor={name}
                        className="underline ml-1 cursor-pointer"
                      >
                        {staticContent?.fileUpload?.browseText || "browse"}
                      </Label>
                    </div>
                    <div className="flex flex-col">
                      {pdfLabel ? (
                        <Label className="text-sm">{pdfLabel}</Label>
                      ) : (
                        <Label className="text-sm">
                          jpg, jpeg, pdf and png. Please upload a file that is
                          less than 5 MB.
                        </Label>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </form>
        ) : null}
        <div className="flex-1 ">
          {Array.isArray(uploadedFiles) &&
            uploadedFiles?.map((fileItem: object, index: number) => (
              <UploadedFileContainer
                key={index}
                fileItem={fileItem}
                fieldItem={fieldItem}
                handleFileDelete={handleFileDelete}
                progressingFileDetail={progressingFileDetail}
                showBorder={
                  uploadedFiles?.length > 1 &&
                  uploadedFiles?.length - 1 !== index
                }
                fileIndex={index}
                watch={watch}
              />
            ))}
          {progressingFileDetail && (
            <div className=" min-h-[58px] flex items-center justify-center">
              <Loader color="primary" />
            </div>
          )}
        </div>
      </div>
      {displaySubInformation && (
        <p className="text-xs text-gray-800">{displaySubInformation}</p>
      )}
      {errorFetchingMessage &&
      !isSuccessOcrFields &&
      watch("ocrReprocessCount") <= maxOcrReprocessCount ? (
        <Label className="mt-5 text-red-500">{errorFetchingMessage}</Label>
      ) : null}
      {error ? <Label className="mt-5 text-red-500">{error}</Label> : null}

      {/* Success Notification Modal */}
      <SuccessNotificationModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
        buttonText="OK"
        isNameSplit={isNameSplit}
        fieldData={fieldItem?.fieldData}
      />

      {/* Error Notification Modal */}
      <ErrorNotificationModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        title="Error"
        message={errorMessage}
        buttonText="OK"
      />

      {/* OCR Processing Modal */}
      <OcrLoaderModal isOpen={showOcrModal} />
    </div>
  );
};

const UploadedFileContainer = ({
  fileIndex,
  fileItem,
  handleFileDelete,
  progressingFileDetail,
  showBorder,
  fieldItem,
  watch,
}: {
  fileIndex: number;
  fileItem: any;
  handleFileDelete: (fileItem: any, fileIndex: number) => void;
  progressingFileDetail: any;
  showBorder: boolean;
  fieldItem?: any;
  watch?: any;
}) => {
  const checkCanDelete = (canDeleteWhen: any) => {
    if (!canDeleteWhen) {
      return true;
    }
    if (watch(canDeleteWhen?.fieldName) === canDeleteWhen?.value) {
      return false;
    }
    return true;
  };

  return (
    <div className="p-5" key={fileIndex}>
      <div
        className={`flex flex-row justify-between border-b-${
          showBorder ? "2" : "0"
        } flex-wrap`}
      >
        <div className="flex flex-row flex-wrap flex-1">
          <ImageIcon className="h-[18px] w-[18px]" />
          <Label className="text-body2 mx-10 truncate max-w-[160px] sm:max-w-[220px]">
            {fileItem?.documentName || fileItem?.name}
          </Label>
          {(fileItem?.documentType === progressingFileDetail?.documentType &&
            fileIndex === progressingFileDetail?.index) || (
            // progressingFileDetail ? (
            //   <Loader color="primary" />
            // ) :
            <div className="h-[10px] w-[10px] rounded-full bg-green-500 flex items-center justify-center self-center">
              <Check className="h-[8px] w-[8px]" color="#ffffff" />
            </div>
          )}
        </div>
        {checkCanDelete(fieldItem?.disableDeleteWhen) && (
          <Trash2
            className="h-[18px] w-[18px] cursor-pointer"
            color="#000000"
            onClick={() => {
              handleFileDelete(fileItem, fileIndex);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default FilePicker;

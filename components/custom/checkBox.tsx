import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Checkbox } from "../ui/checkbox";
import { FieldTitle } from "./FieldTitle";
import { LinkRenderer } from "../custom/linkRender";

interface RadioButtonProps {
  register?: any;
  label?: string;
  fieldItem?: any;
  isMandatory?: boolean;
  selectedValue?: boolean;
  handleChange?: any;
  disabled?: boolean;
  errorMessage?: string;
  markdownText?: string;
}

export function CheckBox(props: RadioButtonProps) {
  const {
    register,
    label,
    fieldItem,
    isMandatory,
    selectedValue,
    handleChange,
    disabled,
    errorMessage,
    markdownText,
  } = props;

  console.log(markdownText);

  return (
    <div className="w-full mb-5 flex items-start">
      <Checkbox
        id={`${"checkbox" + label}`}
        defaultChecked={selectedValue}
        onCheckedChange={(type) => handleChange(type)}
        checked={selectedValue}
        name={fieldItem?.name}
        disabled={disabled}
        className={`rounded-[2px] bg-background border ${errorMessage ? "border-error" : "border-border"
          } text-background data-[state=checked]:text-background data-[state=checked]:bg-primary p-0 mt-1.5`}
      />
      <div className="flex flex-col ml-2">
        {label && (
          <FieldTitle
            label={label}
            htmlFor={`${"checkbox" + label}`}
            isMandatory={!fieldItem?.displayName && isMandatory}
          />
        )}
        {markdownText && (
          <ReactMarkdown
            className="markDown mt-1"
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
            components={{ a: LinkRenderer }}
          >
            {markdownText}
          </ReactMarkdown>
        )}
        {errorMessage && (
          <span className="text-sm text-red-500 mt-1">{errorMessage}</span>
        )}
      </div>
    </div>
  );
}

"use client";

import React, { useState, KeyboardEvent, useRef, useEffect } from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

interface TagInputProps {
  value: string[];
  onChange: (tags: string[]) => void;
  maxTags?: number;
  placeholder?: string;
  className?: string;
  helpTitle?: string;
  helpText?: string;
}

export function TagInput({
  value = [],
  onChange,
  maxTags = Infinity,
  placeholder = "Add tags...",
  className,
  helpTitle,
  helpText,
}: TagInputProps) {
  const [input, setInput] = useState("");
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (!trimmedTag) return;

    if (value.includes(trimmedTag)) {
      setError("This tag already exists");
      return;
    }

    if (value.length >= maxTags) {
      setError(`Maximum ${maxTags} tags allowed`);
      return;
    }

    onChange([...value, trimmedTag]);
    setInput("");
    setError(null);
  };

  const addMultipleTags = (input: string) => {
    const tags = input
      .split(/[,\n]/) // Split by comma or newline
      .map((tag) => tag.trim())
      .filter((tag) => tag.length > 0);

    const uniqueTags = tags.filter((tag) => !value.includes(tag));

    if (value.length + uniqueTags.length > maxTags) {
      setError(
        `Cannot add ${uniqueTags.length} tags. Maximum ${maxTags} tags allowed`
      );
      return;
    }

    if (uniqueTags.length > 0) {
      onChange([...value, ...uniqueTags]);
      setInput("");
      setError(null);
    }
  };

  const removeTag = (indexToRemove: number) => {
    onChange(value.filter((_, index) => index !== indexToRemove));
    setError(null);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (input.includes(",") || input.includes("\n")) {
        addMultipleTags(input);
      } else {
        addTag(input);
      }
    } else if (e.key === "," && !e.shiftKey) {
      e.preventDefault();
      if (input) {
        addMultipleTags(input);
      }
    } else if (e.key === "Backspace" && !input && value.length > 0) {
      removeTag(value.length - 1);
    }
  };

  const handleContainerClick = () => {
    inputRef.current?.focus();
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");
    addMultipleTags(pastedText);
  };

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 3000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [error]);

  return (
    <div className="flex flex-col gap-1 w-full">
      <div
        ref={containerRef}
        onClick={handleContainerClick}
        className={cn(
          "min-h-[42px] w-full flex flex-col p-2 rounded-md border border-input",
          "bg-background focus-within:ring-1 focus-within:ring-ring",
          "hover:border-primary/50 transition-colors",
          className
        )}
      >
        <div className="flex flex-wrap gap-2 w-full">
          {value.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-md text-sm
                       bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
            >
              {tag}
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  removeTag(index);
                }}
                className="inline-flex items-center justify-center w-4 h-4 text-gray-500
                         hover:text-gray-700 transition-colors"
              >
                <X className="w-3.5 h-3.5" />
                <span className="sr-only">Remove tag</span>
              </button>
            </span>
          ))}
        </div>

        <div
          className={`flex items-center w-full ${
            value.length ? "mt-3" : "mt-[0.2rem]"
          } `}
        >
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            placeholder={
              value.length === 0
                ? placeholder
                : "Type and press Enter, or paste comma-separated tags"
            }
            className="flex-1 outline-none bg-transparent text-sm placeholder:text-muted-foreground"
          />
        </div>
      </div>

      <div className="flex justify-between items-center text-xs">
        <div>
          {error && (
            <p className="text-sm text-destructive animate-in fade-in slide-in-from-top-1">
              {error}
            </p>
          )}
        </div>
        {maxTags !== Infinity && (
          <p className="text-muted-foreground">
            {value.length} / {maxTags} tags used
          </p>
        )}
      </div>
      {(helpTitle || helpText) && (
        <p className="text-sm pb-2">
          {helpTitle && <span className="font-bold">{helpTitle}</span>}
          {helpText}
        </p>
      )}
    </div>
  );
}
